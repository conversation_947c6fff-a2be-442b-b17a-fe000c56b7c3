#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل للنظام
يتضمن اختبارات لجميع الوحدات والأنظمة المتقدمة
"""

import sys
import os
import traceback
from datetime import datetime

def test_imports():
    """اختبار استيراد جميع الوحدات الأساسية"""
    print("🔍 اختبار استيراد الوحدات...")
    
    try:
        # اختبار الوحدات الأساسية
        import database
        print("✅ تم استيراد database بنجاح")
        
        import config
        print("✅ تم استيراد config بنجاح")
        
        import logging_config
        print("✅ تم استيراد logging_config بنجاح")
        
        # اختبار الأنظمة المتقدمة
        try:
            import system_initializer
            print("✅ تم استيراد system_initializer بنجاح")
            
            import backup_manager
            print("✅ تم استيراد backup_manager بنجاح")
            
            import security_manager
            print("✅ تم استيراد security_manager بنجاح")
            
            import permissions_manager
            print("✅ تم استيراد permissions_manager بنجاح")
            
            import performance_monitor
            print("✅ تم استيراد performance_monitor بنجاح")
            
        except ImportError as e:
            print(f"⚠️ تحذير: فشل في استيراد بعض الأنظمة المتقدمة: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في استيراد الوحدات: {e}")
        return False

def test_database_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    print("\n🔍 اختبار الاتصال بقاعدة البيانات...")
    
    try:
        from database import get_session, User, init_db
        
        # تهيئة قاعدة البيانات
        init_db()
        print("✅ تم تهيئة قاعدة البيانات بنجاح")
        
        # اختبار إنشاء جلسة
        session = get_session()
        print("✅ تم إنشاء جلسة قاعدة البيانات بنجاح")
        
        # اختبار استعلام بسيط
        user_count = session.query(User).count()
        print(f"✅ عدد المستخدمين في قاعدة البيانات: {user_count}")
        
        session.close()
        print("✅ تم إغلاق الجلسة بنجاح")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {e}")
        traceback.print_exc()
        return False

def test_password_security():
    """اختبار أمان كلمات المرور"""
    print("\n🔍 اختبار أمان كلمات المرور...")
    
    try:
        from database import hash_password, verify_password
        
        # اختبار تشفير كلمة مرور
        test_password = "test123"
        hashed = hash_password(test_password)
        print("✅ تم تشفير كلمة المرور بنجاح")
        
        # اختبار التحقق من كلمة المرور
        is_valid = verify_password(hashed, test_password)
        if is_valid:
            print("✅ تم التحقق من كلمة المرور بنجاح")
        else:
            print("❌ فشل في التحقق من كلمة المرور")
            return False
        
        # اختبار كلمة مرور خاطئة
        is_invalid = verify_password(hashed, "wrong_password")
        if not is_invalid:
            print("✅ تم رفض كلمة المرور الخاطئة بنجاح")
        else:
            print("❌ تم قبول كلمة مرور خاطئة!")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار أمان كلمات المرور: {e}")
        traceback.print_exc()
        return False

def test_user_management():
    """اختبار إدارة المستخدمين"""
    print("\n🔍 اختبار إدارة المستخدمين...")
    
    try:
        from database import get_session, add_user, authenticate_user, User
        
        session = get_session()
        
        # اختبار إضافة مستخدم جديد
        test_username = f"test_user_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        success, message = add_user(
            session, 
            username=test_username,
            password="test123",
            full_name="مستخدم اختبار",
            email="<EMAIL>",
            role="user"
        )
        
        if success:
            print("✅ تم إضافة مستخدم اختبار بنجاح")
        else:
            print(f"❌ فشل في إضافة مستخدم اختبار: {message}")
            session.close()
            return False
        
        # اختبار تسجيل الدخول
        user, auth_message = authenticate_user(session, test_username, "test123")
        if user:
            print("✅ تم تسجيل الدخول بنجاح")
        else:
            print(f"❌ فشل في تسجيل الدخول: {auth_message}")
            session.close()
            return False
        
        # حذف المستخدم التجريبي
        test_user = session.query(User).filter_by(username=test_username).first()
        if test_user:
            session.delete(test_user)
            session.commit()
            print("✅ تم حذف مستخدم الاختبار")
        
        session.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار إدارة المستخدمين: {e}")
        traceback.print_exc()
        return False

def test_advanced_systems():
    """اختبار الأنظمة المتقدمة"""
    print("\n🔍 اختبار الأنظمة المتقدمة...")
    
    try:
        # اختبار system_initializer
        try:
            from system_initializer import initialize_advanced_systems, get_systems_status
            
            # تهيئة الأنظمة
            systems = initialize_advanced_systems()
            if systems:
                print("✅ تم تهيئة الأنظمة المتقدمة بنجاح")
                
                # عرض حالة الأنظمة
                status = get_systems_status()
                print(f"📊 حالة الأنظمة: {len([s for s in status.values() if s])} من {len(status)} نشط")
            else:
                print("⚠️ تحذير: لم يتم تهيئة الأنظمة المتقدمة")
                
        except ImportError:
            print("⚠️ تحذير: الأنظمة المتقدمة غير متاحة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الأنظمة المتقدمة: {e}")
        traceback.print_exc()
        return False

def test_ui_modules():
    """اختبار وحدات واجهة المستخدم"""
    print("\n🔍 اختبار وحدات واجهة المستخدم...")
    
    try:
        # اختبار استيراد الوحدات الأساسية
        ui_modules = [
            'ui.main_window',
            'ui.clients',
            'ui.suppliers', 
            'ui.employees',
            'ui.invoices',
            'ui.inventory',
            'ui.expenses',
            'ui.revenues',
            'ui.projects',
            'ui.reports'
        ]
        
        imported_count = 0
        for module in ui_modules:
            try:
                __import__(module)
                imported_count += 1
                print(f"✅ تم استيراد {module}")
            except Exception as e:
                print(f"⚠️ تحذير: فشل في استيراد {module}: {e}")
        
        print(f"📊 تم استيراد {imported_count} من {len(ui_modules)} وحدة واجهة مستخدم")
        return imported_count > len(ui_modules) // 2  # نجح إذا تم استيراد أكثر من النصف
        
    except Exception as e:
        print(f"❌ خطأ في اختبار وحدات واجهة المستخدم: {e}")
        return False

def run_comprehensive_test():
    """تشغيل الاختبار الشامل"""
    print("🚀 بدء الاختبار الشامل للنظام")
    print("=" * 50)
    
    test_results = []
    
    # تشغيل جميع الاختبارات
    tests = [
        ("استيراد الوحدات", test_imports),
        ("الاتصال بقاعدة البيانات", test_database_connection),
        ("أمان كلمات المرور", test_password_security),
        ("إدارة المستخدمين", test_user_management),
        ("الأنظمة المتقدمة", test_advanced_systems),
        ("وحدات واجهة المستخدم", test_ui_modules)
    ]
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            test_results.append((test_name, result))
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
            test_results.append((test_name, False))
    
    # عرض النتائج النهائية
    print("\n" + "=" * 50)
    print("📊 نتائج الاختبار الشامل:")
    print("=" * 50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ نجح" if result else "❌ فشل"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print("=" * 50)
    print(f"📈 النتيجة النهائية: {passed}/{total} اختبار نجح ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! النظام يعمل بشكل مثالي")
    elif passed >= total * 0.8:
        print("✅ معظم الاختبارات نجحت. النظام يعمل بشكل جيد")
    else:
        print("⚠️ بعض الاختبارات فشلت. يحتاج النظام إلى مراجعة")
    
    return passed, total

if __name__ == "__main__":
    run_comprehensive_test()
