#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
محسن قاعدة البيانات المتقدم
==========================

أداة شاملة لتحسين أداء قاعدة البيانات وإضافة فهارس متقدمة
"""

from database import get_session, engine
from sqlalchemy import text, inspect
import time

def analyze_database_performance():
    """تحليل أداء قاعدة البيانات الحالي"""
    print("🔍 تحليل أداء قاعدة البيانات...")
    
    session = get_session()
    try:
        # تحليل حجم قاعدة البيانات
        result = session.execute(text("PRAGMA page_count")).fetchone()
        page_count = result[0] if result else 0
        
        result = session.execute(text("PRAGMA page_size")).fetchone()
        page_size = result[0] if result else 0
        
        db_size_mb = (page_count * page_size) / (1024 * 1024)
        print(f"📊 حجم قاعدة البيانات: {db_size_mb:.2f} MB")
        
        # تحليل الجداول
        inspector = inspect(engine)
        tables = inspector.get_table_names()
        print(f"📊 عدد الجداول: {len(tables)}")
        
        # تحليل الفهارس
        total_indexes = 0
        for table in tables:
            indexes = inspector.get_indexes(table)
            total_indexes += len(indexes)
        
        print(f"📊 عدد الفهارس: {total_indexes}")
        
        # تحليل أداء الاستعلامات الشائعة
        print("\n🔍 تحليل أداء الاستعلامات الشائعة:")
        
        # اختبار استعلام العملاء
        start_time = time.time()
        result = session.execute(text("SELECT COUNT(*) FROM clients")).fetchone()
        clients_time = time.time() - start_time
        print(f"   - استعلام العملاء: {clients_time*1000:.2f}ms ({result[0]} عميل)")
        
        # اختبار استعلام الفواتير
        start_time = time.time()
        result = session.execute(text("SELECT COUNT(*) FROM invoices")).fetchone()
        invoices_time = time.time() - start_time
        print(f"   - استعلام الفواتير: {invoices_time*1000:.2f}ms ({result[0]} فاتورة)")
        
        # اختبار استعلام المخزون
        start_time = time.time()
        result = session.execute(text("SELECT COUNT(*) FROM inventory")).fetchone()
        inventory_time = time.time() - start_time
        print(f"   - استعلام المخزون: {inventory_time*1000:.2f}ms ({result[0]} عنصر)")
        
        return {
            'db_size_mb': db_size_mb,
            'tables_count': len(tables),
            'indexes_count': total_indexes,
            'clients_time': clients_time,
            'invoices_time': invoices_time,
            'inventory_time': inventory_time
        }
        
    except Exception as e:
        print(f"❌ خطأ في تحليل الأداء: {e}")
        return {}
    finally:
        session.close()

def create_advanced_indexes():
    """إنشاء فهارس متقدمة لتحسين الأداء"""
    print("🔧 إنشاء فهارس متقدمة...")
    
    session = get_session()
    try:
        # فهارس العملاء المتقدمة
        advanced_indexes = [
            # فهارس العملاء
            "CREATE INDEX IF NOT EXISTS idx_clients_name_search ON clients(name COLLATE NOCASE)",
            "CREATE INDEX IF NOT EXISTS idx_clients_phone_search ON clients(phone)",
            "CREATE INDEX IF NOT EXISTS idx_clients_balance_status ON clients(balance, is_active)",
            "CREATE INDEX IF NOT EXISTS idx_clients_created_date ON clients(created_at)",
            
            # فهارس الفواتير المتقدمة
            "CREATE INDEX IF NOT EXISTS idx_invoices_client_date ON invoices(client_id, invoice_date)",
            "CREATE INDEX IF NOT EXISTS idx_invoices_total_amount ON invoices(total_amount)",
            "CREATE INDEX IF NOT EXISTS idx_invoices_status_date ON invoices(status, invoice_date)",
            "CREATE INDEX IF NOT EXISTS idx_invoices_payment_status ON invoices(payment_status)",
            
            # فهارس المخزون المتقدمة
            "CREATE INDEX IF NOT EXISTS idx_inventory_name_search ON inventory(name COLLATE NOCASE)",
            "CREATE INDEX IF NOT EXISTS idx_inventory_category_quantity ON inventory(category, quantity)",
            "CREATE INDEX IF NOT EXISTS idx_inventory_price_range ON inventory(selling_price)",
            "CREATE INDEX IF NOT EXISTS idx_inventory_low_stock ON inventory(quantity, min_quantity)",
            
            # فهارس المصروفات
            "CREATE INDEX IF NOT EXISTS idx_expenses_date_amount ON expenses(expense_date, amount)",
            "CREATE INDEX IF NOT EXISTS idx_expenses_category_date ON expenses(category, expense_date)",
            "CREATE INDEX IF NOT EXISTS idx_expenses_client_project ON expenses(client_id, project_id)",
            
            # فهارس الإيرادات
            "CREATE INDEX IF NOT EXISTS idx_revenues_date_amount ON revenues(revenue_date, amount)",
            "CREATE INDEX IF NOT EXISTS idx_revenues_source_date ON revenues(source, revenue_date)",
            "CREATE INDEX IF NOT EXISTS idx_revenues_client_project ON revenues(client_id, project_id)",
            
            # فهارس المشاريع
            "CREATE INDEX IF NOT EXISTS idx_projects_client_status ON projects(client_id, status)",
            "CREATE INDEX IF NOT EXISTS idx_projects_start_end_date ON projects(start_date, end_date)",
            "CREATE INDEX IF NOT EXISTS idx_projects_budget_range ON projects(budget)",
            
            # فهارس المبيعات
            "CREATE INDEX IF NOT EXISTS idx_sales_date_total ON sales(sale_date, total_amount)",
            "CREATE INDEX IF NOT EXISTS idx_sales_client_date ON sales(client_id, sale_date)",
            
            # فهارس المشتريات
            "CREATE INDEX IF NOT EXISTS idx_purchases_date_total ON purchases(purchase_date, total_amount)",
            "CREATE INDEX IF NOT EXISTS idx_purchases_supplier_date ON purchases(supplier_id, purchase_date)",
            
            # فهارس الموردين
            "CREATE INDEX IF NOT EXISTS idx_suppliers_name_search ON suppliers(name COLLATE NOCASE)",
            "CREATE INDEX IF NOT EXISTS idx_suppliers_balance_status ON suppliers(balance, is_active)",
            
            # فهارس الموظفين
            "CREATE INDEX IF NOT EXISTS idx_employees_name_search ON employees(name COLLATE NOCASE)",
            "CREATE INDEX IF NOT EXISTS idx_employees_department_position ON employees(department, position)",
            "CREATE INDEX IF NOT EXISTS idx_employees_salary_range ON employees(salary)",
            
            # فهارس المستخدمين
            "CREATE INDEX IF NOT EXISTS idx_users_username_active ON users(username, is_active)",
            "CREATE INDEX IF NOT EXISTS idx_users_role_active ON users(role, is_active)",
            "CREATE INDEX IF NOT EXISTS idx_users_last_login ON users(last_login)",
        ]
        
        created_count = 0
        for index_sql in advanced_indexes:
            try:
                session.execute(text(index_sql))
                created_count += 1
            except Exception as e:
                print(f"⚠️ تحذير في إنشاء فهرس: {e}")
        
        session.commit()
        print(f"✅ تم إنشاء {created_count} فهرس متقدم")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الفهارس: {e}")
        session.rollback()
    finally:
        session.close()

def optimize_database_settings():
    """تحسين إعدادات قاعدة البيانات"""
    print("⚙️ تحسين إعدادات قاعدة البيانات...")
    
    session = get_session()
    try:
        # إعدادات الأداء المتقدمة
        optimization_commands = [
            "PRAGMA optimize",
            "PRAGMA analysis_limit=1000",
            "PRAGMA cache_size=-128000",  # 128MB cache
            "PRAGMA temp_store=MEMORY",
            "PRAGMA mmap_size=536870912",  # 512MB memory mapping
            "PRAGMA journal_mode=WAL",
            "PRAGMA synchronous=NORMAL",
            "PRAGMA wal_autocheckpoint=1000",
            "PRAGMA busy_timeout=30000",
            "PRAGMA foreign_keys=ON",
            "ANALYZE"
        ]
        
        for command in optimization_commands:
            try:
                session.execute(text(command))
                print(f"✅ تم تنفيذ: {command}")
            except Exception as e:
                print(f"⚠️ تحذير في تنفيذ {command}: {e}")
        
        session.commit()
        print("✅ تم تحسين إعدادات قاعدة البيانات")
        
    except Exception as e:
        print(f"❌ خطأ في تحسين الإعدادات: {e}")
    finally:
        session.close()

def vacuum_and_reindex():
    """تنظيف وإعادة فهرسة قاعدة البيانات"""
    print("🧹 تنظيف وإعادة فهرسة قاعدة البيانات...")
    
    session = get_session()
    try:
        # تنظيف المساحة المهدرة
        session.execute(text("VACUUM"))
        print("✅ تم تنظيف المساحة المهدرة")
        
        # إعادة بناء الفهارس
        session.execute(text("REINDEX"))
        print("✅ تم إعادة بناء الفهارس")
        
        # تحديث الإحصائيات
        session.execute(text("ANALYZE"))
        print("✅ تم تحديث الإحصائيات")
        
    except Exception as e:
        print(f"❌ خطأ في التنظيف: {e}")
    finally:
        session.close()

def run_complete_optimization():
    """تشغيل تحسين شامل لقاعدة البيانات"""
    print("🚀 بدء التحسين الشامل لقاعدة البيانات")
    print("=" * 50)
    
    # تحليل الأداء قبل التحسين
    print("📊 تحليل الأداء قبل التحسين:")
    before_stats = analyze_database_performance()
    
    print("\n" + "=" * 50)
    
    # تطبيق التحسينات
    create_advanced_indexes()
    optimize_database_settings()
    vacuum_and_reindex()
    
    print("\n" + "=" * 50)
    
    # تحليل الأداء بعد التحسين
    print("📊 تحليل الأداء بعد التحسين:")
    after_stats = analyze_database_performance()
    
    # مقارنة النتائج
    if before_stats and after_stats:
        print("\n📈 مقارنة الأداء:")
        
        if 'clients_time' in before_stats and 'clients_time' in after_stats:
            improvement = ((before_stats['clients_time'] - after_stats['clients_time']) / before_stats['clients_time']) * 100
            print(f"   - تحسن استعلام العملاء: {improvement:.1f}%")
        
        if 'invoices_time' in before_stats and 'invoices_time' in after_stats:
            improvement = ((before_stats['invoices_time'] - after_stats['invoices_time']) / before_stats['invoices_time']) * 100
            print(f"   - تحسن استعلام الفواتير: {improvement:.1f}%")
        
        if 'inventory_time' in before_stats and 'inventory_time' in after_stats:
            improvement = ((before_stats['inventory_time'] - after_stats['inventory_time']) / before_stats['inventory_time']) * 100
            print(f"   - تحسن استعلام المخزون: {improvement:.1f}%")
    
    print("\n🎉 تم إكمال التحسين الشامل لقاعدة البيانات!")

if __name__ == "__main__":
    run_complete_optimization()
